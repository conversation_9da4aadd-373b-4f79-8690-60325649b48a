/* 🎰 VEGAS ACE SLOTS - Modern Casino UI 2025 */

:root {
    /* Color Palette */
    --primary-dark: #0D0D1F;
    --secondary-dark: #1A1A2E;
    --accent-gold: #FFD700;
    --accent-purple: #9B30FF;
    --accent-red: #FF3366;
    --neon-blue: #4DFFFF;
    
    /* Gradients */
    --premium-gradient: linear-gradient(135deg, #2A1B3D 0%, #1A1A2E 100%);
    --gold-gradient: linear-gradient(90deg, #FFD700 0%, #FFA500 100%);
    --button-gradient: linear-gradient(45deg, #4A154B 0%, #9B30FF 100%);
    
    /* Shadows */
    --premium-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
    --neon-shadow: 0 0 15px rgba(77, 255, 255, 0.5);
    --text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
}

/* Reset & Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Poppins', sans-serif;
}

body {
    background: var(--primary-dark);
    color: white;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Glassmorphism Effect */
.glass-panel {
    background: rgba(26, 26, 46, 0.8);
    backdrop-filter: blur(12px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    box-shadow: var(--premium-shadow);
}

/* Game Container */
.game-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
    position: relative;
}

/* Header Section */
.header {
    padding: 15px 25px;
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-radius: 12px;
    background: var(--premium-gradient);
}

.title {
    font-size: 2.5rem;
    font-weight: 800;    background: var(--gold-gradient);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    text-shadow: var(--text-shadow);
}

/* Stats Display */
.stats {
    display: flex;
    gap: 20px;
}

.stat {
    padding: 10px 20px;
    border-radius: 8px;
    background: rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 215, 0, 0.2);
    backdrop-filter: blur(5px);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stat:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
}

.stat-label {
    font-size: 0.8rem;
    color: #888;
    margin-bottom: 4px;
}

.stat-value {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--accent-gold);
}

/* Slot Machine */
.slot-machine {
    background: var(--secondary-dark);
    padding: 30px;
    border-radius: 20px;
    box-shadow: var(--premium-shadow);
    margin: 20px 0;
    position: relative;
    overflow: hidden;
}

.slot-machine:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 100px;
    background: linear-gradient(
        to bottom,
        var(--secondary-dark) 0%,
        transparent 100%
    );
    z-index: 1;
    pointer-events: none;
}

.slot-machine:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 100px;
    background: linear-gradient(
        to top,
        var(--secondary-dark) 0%,
        transparent 100%
    );
    z-index: 1;
    pointer-events: none;
}

.reels {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 10px;
    padding: 20px;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 12px;
    border: 2px solid rgba(255, 215, 0, 0.1);
}

/* Symbols */
.symbol {
    aspect-ratio: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 8px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.symbol.winning {
    animation: winPulse 1s infinite, neonGlow 1.5s infinite;
    box-shadow: 0 0 20px var(--neon-blue);
}

.symbol:after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(
        to bottom right,
        rgba(255, 215, 0, 0) 0%,
        rgba(255, 215, 0, 0.13) 50%,
        rgba(255, 215, 0, 0) 100%
    );
    transform: rotate(45deg);
    transition: all 0.3s ease;
}

.symbol:hover:after {
    transform: rotate(45deg) translate(50%, 50%);
}

/* Controls Section */
.controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background: var(--premium-gradient);
    border-radius: 12px;
    margin-top: 20px;
}

/* Buttons */
.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    background: var(--button-gradient);
    color: white;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--neon-shadow);
}

.spin-btn {
    padding: 20px 40px;
    font-size: 1.2rem;
    background: var(--gold-gradient);
    color: var(--primary-dark);
    position: relative;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.spin-btn:before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 70%);
    transform: rotate(45deg);
    animation: shine 3s infinite;
}

@keyframes shine {
    0% { transform: translateX(-100%) rotate(45deg); }
    100% { transform: translateX(100%) rotate(45deg); }
}

/* Bonus Displays */
.bonus-displays {
    position: absolute;
    top: 20px;
    right: 20px;
    display: flex;
    gap: 15px;
}

.bonus-display {
    padding: 15px;
    border-radius: 10px;
    background: var(--premium-gradient);
    border: 1px solid var(--accent-gold);
}

/* Win Popup */
.win-popup {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    padding: 30px;
    text-align: center;
    z-index: 1000;
    animation: floatAnimation 3s infinite ease-in-out;
    backdrop-filter: blur(10px);
}

.win-amount {
    font-size: 3rem;
    font-weight: 800;
    color: var(--accent-gold);
    text-shadow: var(--text-shadow);
    margin-bottom: 10px;
    animation: neonGlow 2s infinite;
}

/* Authentication UI */
.auth-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.auth-container {
    width: 100%;
    max-width: 400px;
    padding: 30px;
    background: var(--premium-gradient);
    border-radius: 20px;
    box-shadow: var(--premium-shadow);
    border: 1px solid rgba(255, 215, 0, 0.1);
}

.auth-header {
    text-align: center;
    margin-bottom: 30px;
}

.auth-header h1 {
    font-size: 2rem;
    margin-bottom: 10px;
    background: var(--gold-gradient);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
}

.auth-form {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.auth-form h2 {
    color: var(--accent-gold);
    margin-bottom: 20px;
    text-align: center;
}

.input-group {
    position: relative;
}

.input-group input {
    width: 100%;
    padding: 12px 15px;
    background: rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 215, 0, 0.2);
    border-radius: 8px;
    color: white;
    font-size: 1rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(5px);
}

.input-group input:focus {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
    outline: none;
    border-color: var(--accent-gold);
    box-shadow: 0 0 10px rgba(255, 215, 0, 0.2);
}

.auth-btn {
    padding: 15px;
    background: var(--gold-gradient);
    border: none;
    border-radius: 8px;
    color: var(--primary-dark);
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
}

.auth-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--neon-shadow);
}

.auth-link {
    text-align: center;
    color: #888;
    margin-top: 15px;
}

.auth-link span {
    color: var(--accent-gold);
    cursor: pointer;
    transition: all 0.3s ease;
}

.auth-link span:hover {
    text-shadow: var(--text-shadow);
}

/* Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-content {
    width: 100%;
    max-width: 500px;
    background: var(--premium-gradient);
    border-radius: 20px;
    box-shadow: var(--premium-shadow);
    border: 1px solid rgba(255, 215, 0, 0.1);
    position: relative;
}

/* Profile UI */
.profile-container {
    padding: 30px;
    background: linear-gradient(
        135deg,
        rgba(42, 27, 61, 0.9) 0%,
        rgba(26, 26, 46, 0.9) 100%
    );
    backdrop-filter: blur(10px);
}

.profile-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
}

.close-btn {
    background: none;
    border: none;
    color: #888;
    font-size: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.close-btn:hover {
    color: var(--accent-red);
}

.profile-info {
    display: grid;
    gap: 25px;
    margin-bottom: 30px;
}

.balance-section {
    text-align: center;
    padding: 20px;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 12px;
    border: 1px solid rgba(255, 215, 0, 0.1);
}

.balance-display-large {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--accent-gold);
    margin: 15px 0;
    text-shadow: var(--text-shadow);
}

.balance-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 20px;
}

/* Transaction UI */
.transaction-container {
    padding: 30px;
}

.payment-options {
    display: grid;
    gap: 15px;
    margin: 20px 0;
}

.payment-option {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.payment-option:hover {
    background: rgba(255, 215, 0, 0.1);
}

.payment-option input[type="radio"] {
    accent-color: var(--accent-gold);
}

/* Action Buttons */
.action-btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
}

.action-btn.deposit {
    background: var(--gold-gradient);
    color: var(--primary-dark);
}

.action-btn.withdraw {
    background: var(--button-gradient);
    color: white;
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--neon-shadow);
}

/* Message Display */
.message-display {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    padding: 15px 30px;
    background: var(--premium-gradient);
    border-radius: 8px;
    box-shadow: var(--premium-shadow);
    border: 1px solid var(--accent-gold);
    z-index: 1100;
    text-align: center;
}

/* Animations */
@keyframes winPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

@keyframes neonGlow {
    0% { box-shadow: 0 0 5px var(--accent-gold), 0 0 10px var(--accent-gold), 0 0 15px var(--accent-gold); }
    50% { box-shadow: 0 0 10px var(--accent-gold), 0 0 20px var(--accent-gold), 0 0 30px var(--accent-gold); }
    100% { box-shadow: 0 0 5px var(--accent-gold), 0 0 10px var(--accent-gold), 0 0 15px var(--accent-gold); }
}

@keyframes floatAnimation {
    0% { transform: translateY(0); }
    50% { transform: translateY(-10px); }
    100% { transform: translateY(0); }
}

@keyframes spinButton {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); box-shadow: 0 0 20px var(--accent-gold); }
    100% { transform: scale(1); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .header {
        flex-direction: column;
        gap: 15px;
    }
    
    .stats {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .controls {
        flex-direction: column;
        gap: 15px;
    }
    
    .spin-btn {
        width: 100%;
    }
    
    .symbol {
        font-size: 2rem;
    }
}

/* Dark Mode Enhancement */
@media (prefers-color-scheme: dark) {
    :root {
        --primary-dark: #050510;
        --secondary-dark: #0D0D1F;
    }
}